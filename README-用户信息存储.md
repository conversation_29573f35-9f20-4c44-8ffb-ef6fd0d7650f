# 用户信息存储实现完成

## 功能概述

已按照需求完成整个登录后用户信息的存储和读取逻辑，包括：

- ✅ 用户token（userWxToken）存储和获取
- ✅ 用户昵称（nickName）、头像（avatarUrl）、性别（gender）管理
- ✅ 默认值处理（"微信用户"和灰色头像）
- ✅ Pinia和Storage双重存储
- ✅ 退出登录清空逻辑
- ✅ 微信小程序手动获取更新功能

## 如何存储

### 登录时自动存储
```typescript
// 登录成功后自动调用
await tokenStore.handleLoginResponse(loginResponse, loginData)
```

### 手动更新用户资料
```typescript
const userStore = useUserStore()
userStore.updateUserProfile('新昵称', '新头像URL')
```

## 如何获取

### 1. 获取Token（推荐）
```typescript
import { getUserWxToken } from '@/utils/auth'
const token = getUserWxToken()
```

### 2. 获取用户信息
```typescript
import { useUserStore } from '@/store/user'
const userStore = useUserStore()
const userInfo = userStore.userInfo
```

### 3. 检查登录状态
```typescript
import { isUserLoggedIn } from '@/utils/auth'
const loggedIn = isUserLoggedIn()
```

### 4. 从Storage直接获取
```typescript
const userInfo = uni.getStorageSync('userInfo')
const token = userInfo?.userWxToken || ''
```

## 退出登录

```typescript
const tokenStore = useTokenStore()
await tokenStore.logout() // 自动清空所有用户信息
```

## 数据结构

```typescript
interface IUserInfo {
  nickName: string      // 用户昵称，默认"微信用户"
  avatarUrl: string     // 用户头像，默认灰色头像路径
  gender: string        // 用户性别，"0"未知/"1"男/"2"女
  userWxToken: string   // 用户token
}
```

## 存储位置

- **Pinia Store**: `useUserStore().userInfo`
- **Storage Key**: `userInfo`

## 默认值

- nickName: "微信用户"
- avatarUrl: "/static/images/default-avatar.png"
- gender: "0"
- userWxToken: ""

## 已更新的文件

1. `src/store/user.ts` - 用户信息管理（重构）
2. `src/store/token.ts` - Token处理（简化）
3. `src/utils/auth.ts` - 认证工具函数（新增）
4. `src/pages/login/login.vue` - 登录页面（更新）
5. `src/pages/profile/profile.vue` - 个人资料页面（更新）
6. `src/http/interceptor.ts` - HTTP拦截器（更新）
7. `src/utils/tokenDebug.ts` - 调试工具（更新）

## 测试

可以使用测试页面验证功能：`src/pages/test/user-info-test.vue`

## 特点

- **代码简洁优雅**: 统一的用户信息管理
- **响应式**: 数据变化自动更新UI
- **持久化**: 自动同步Pinia和Storage
- **不影响其他逻辑**: 保持向后兼容
- **工具函数**: 提供便捷的获取方法

所有功能已完成并测试通过！
