import { useUserStore } from '@/store/user'

/**
 * 获取用户token的工具函数
 * @returns 用户token字符串，如果未登录返回空字符串
 */
export function getUserWxToken(): string {
  const userStore = useUserStore()
  return userStore.getUserWxToken
}

/**
 * 检查用户是否已登录
 * @returns 是否已登录
 */
export function isUserLoggedIn(): boolean {
  const userStore = useUserStore()
  return userStore.isLoggedIn
}

/**
 * 获取用户信息
 * @returns 用户信息对象
 */
export function getUserInfo() {
  const userStore = useUserStore()
  return userStore.userInfo
}

/**
 * 从storage直接获取用户token（不依赖store实例）
 * @returns 用户token字符串
 */
export function getUserWxTokenFromStorage(): string {
  try {
    const userInfo = uni.getStorageSync('userInfo')
    if (userInfo && typeof userInfo === 'object') {
      return userInfo.userWxToken || ''
    }
    return ''
  }
  catch (error) {
    console.error('从storage获取token失败:', error)
    return ''
  }
}
