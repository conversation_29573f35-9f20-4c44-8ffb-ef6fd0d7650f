<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "个人信息"
  }
}
</route>

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { useUserStore } from '@/store/user'

interface ChooseAvatarEvent {
  detail: {
    avatarUrl: string
  }
}

interface NicknameInputEvent {
  detail: {
    value: string
  }
}

const userStore = useUserStore()

// 使用store中的用户信息
const userInfo = computed(() => userStore.userInfo)
const userAvatar = ref(userInfo.value.avatarUrl)
const tempNickname = ref(userInfo.value.nickName)

// 登录状态
const isLoggedIn = computed(() => userStore.isLoggedIn)

onMounted(() => {
  // 同步store中的数据到本地变量
  userAvatar.value = userInfo.value.avatarUrl
  tempNickname.value = userInfo.value.nickName

  // 处理页面参数（保持原有逻辑）
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.avatar) {
    const decodedAvatar = decodeURIComponent(options.avatar)
    userAvatar.value = decodedAvatar
  }
  if (options.nickname) {
    const decodedNickname = decodeURIComponent(options.nickname)
    tempNickname.value = decodedNickname
  }
})

function onChooseAvatar(e: ChooseAvatarEvent) {
  userAvatar.value = e.detail.avatarUrl
}

function onNicknameInput(e: NicknameInputEvent) {
  tempNickname.value = e.detail.value
}

function handleSave() {
  const nickname = tempNickname.value.trim()
  if (!nickname) {
    uni.showToast({ title: '请输入昵称', icon: 'none' })
    return
  }

  // 使用store的方法更新用户信息
  userStore.updateUserProfile(nickname, userAvatar.value)

  uni.showToast({ title: '保存成功', icon: 'success' })

  setTimeout(() => {
    const eventChannel = (uni as any).getOpenerEventChannel?.()
    eventChannel?.emit('updateUserInfo', {
      avatar: userInfo.value.avatarUrl,
      nickname: userInfo.value.nickName,
    })
    uni.navigateBack()
  }, 1000)
}
</script>

<template>
  <view class="min-h-screen flex flex-col bg-gray-100">
    <view class="flex-1 p-6">
      <!-- 头像区域 -->
      <view class="mb-10 flex justify-center">
        <button
          class="relative h-[150rpx] w-[150rpx] center rounded-full bg-transparent p-0 after:border-none"
          open-type="chooseAvatar"
          @chooseavatar="onChooseAvatar"
        >
          <image
            class="h-full w-full border-4 border-white rounded-full bg-gray-200 shadow-lg"
            :src="userAvatar"
            mode="aspectFill"
          />
        </button>
      </view>

      <view class="rounded-xl bg-white p-4 shadow-sm">
        <view class="flex items-center py-3">
          <view class="w-20 text-base text-gray-700">
            昵称
          </view>
          <view class="flex-1">
            <input
              v-model="tempNickname"
              type="nickname"
              class="text-right text-base text-gray-800 placeholder-gray-400"
              placeholder="请输入昵称"
              @input="onNicknameInput"
            >
          </view>
        </view>
      </view>

      <view class="mt-12 px-4">
        <button
          class="h-12 w-full center rounded-full text-lg text-white font-medium shadow-md transition-transform active:scale-95"
          style="background: linear-gradient(135deg, #4ade80 0%, #06b6d4 100%)"
          @click="handleSave"
        >
          保存
        </button>
      </view>
    </view>
  </view>
</template>

<style scoped>
input {
  border: none;
  background-color: transparent;
  padding: 0;
  outline: none;
}
</style>
