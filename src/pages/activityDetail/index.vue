<route lang="jsonc" type="page">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "活动详情"
  }
}
</route>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'

// 活动详情数据
const activityDetail = ref({
  id: 1,
  images: [
    'https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/dev/2025/09/el1756803669608.jpg',
    'https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/dev/2025/09/el1756803669608.jpg',
    'https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/dev/2025/09/el1756803669608.jpg',
  ],
  title: '健康生活分享会',
  time: '2024-01-15 14:00-16:00',
  location: '北京市朝阳区健康中心3楼会议室',
  description: '本次活动将邀请专业的健康管理师为大家分享健康生活的理念和实践方法，包括合理饮食、适量运动、心理健康等方面的内容。',
  marketPrice: 199,
  memberPrice: 99,
  nonMemberPrice: 149,
  totalSlots: 15,
  remainingSlots: 3,
  registrationProgress: 80, // 报名进度百分比
  status: 'ongoing', // ongoing, full, ended
  organizer: '不老联盟健康中心',
  tags: ['健康', '生活方式', '专业指导'],
})

// 轮播图当前索引
const currentSwiperIndex = ref(0)

// 是否已报名
const isRegistered = ref(false)

// 页面参数
const activityId = ref('')

// 获取活动详情
async function getActivityDetail(id: string) {
  // 这里应该调用API获取活动详情
  console.log('获取活动详情:', id)
  // 模拟API调用
}

// 轮播图切换
function onSwiperChange(e: any) {
  currentSwiperIndex.value = e.detail.current
}

// 报名活动
function handleRegister() {
  if (activityDetail.value.remainingSlots <= 0) {
    uni.showToast({
      title: '名额已满',
      icon: 'none',
    })
    return
  }

  if (isRegistered.value) {
    uni.showToast({
      title: '您已报名此活动',
      icon: 'none',
    })
    return
  }

  uni.showModal({
    title: '确认报名',
    content: `确定要报名参加"${activityDetail.value.title}"吗？`,
    success: (res) => {
      if (res.confirm) {
        // 这里应该调用报名API
        isRegistered.value = true
        activityDetail.value.remainingSlots--
        activityDetail.value.registrationProgress = Math.round(
          ((activityDetail.value.totalSlots - activityDetail.value.remainingSlots) / activityDetail.value.totalSlots) * 100,
        )
        uni.showToast({
          title: '报名成功',
          icon: 'success',
        })
      }
    },
  })
}

// 分享活动
function handleShare() {
  uni.showActionSheet({
    itemList: ['分享给朋友', '分享到朋友圈', '复制链接'],
    success: (res) => {
      const actions = ['分享给朋友', '分享到朋友圈', '复制链接']
      uni.showToast({
        title: actions[res.tapIndex],
        icon: 'none',
      })
    },
  })
}

// 获取报名按钮文本
function getRegisterButtonText() {
  if (activityDetail.value.remainingSlots <= 0) {
    return '名额已满'
  }
  if (isRegistered.value) {
    return '已报名'
  }
  return '立即报名'
}

// 获取报名按钮样式
function getRegisterButtonClass() {
  if (activityDetail.value.remainingSlots <= 0 || isRegistered.value) {
    return 'register-btn-disabled'
  }
  return 'register-btn-active'
}

onLoad((options) => {
  if (options?.id) {
    activityId.value = options.id
    getActivityDetail(options.id)
  }
})
</script>

<template>
  <view class="activity-detail-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-title">
          <view class="navbar-left">
            <wd-icon name="thin-arrow-left" size="20rpx" />
          </view>
          活动详情
        </view>
      </view>
    </view>

    <!-- 活动图片轮播 -->
    <view class="image-section">
      <swiper
        class="activity-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500"
        @change="onSwiperChange"
      >
        <swiper-item v-for="(image, index) in activityDetail.images" :key="index">
          <image :src="image" class="swiper-image" mode="aspectFill" />
        </swiper-item>
      </swiper>
      <view class="image-indicator">
        {{ currentSwiperIndex + 1 }}/{{ activityDetail.images.length }}
      </view>
    </view>

    <!-- 活动信息 -->
    <view class="content-section">
      <!-- 活动基本信息 -->
      <view class="info-card">
        <view class="activity-title">
          {{ activityDetail.title }}
        </view>
        <view class="activity-tags">
          <text v-for="(tag, index) in activityDetail.tags" :key="index" class="tag">
            {{ tag }}
          </text>
        </view>

        <view class="info-item">
          <uni-icons type="calendar" size="16" color="#666" />
          <text class="info-text">{{ activityDetail.time }}</text>
        </view>

        <view class="info-item">
          <uni-icons type="location" size="16" color="#666" />
          <text class="info-text">{{ activityDetail.location }}</text>
        </view>

        <view class="info-item">
          <uni-icons type="person" size="16" color="#666" />
          <text class="info-text">{{ activityDetail.organizer }}</text>
        </view>
      </view>

      <!-- 价格信息 -->
      <view class="price-card">
        <view class="price-title">
          报名费用
        </view>
        <view class="price-content">
          <view class="price-item">
            <text class="price-label">门市价</text>
            <text class="price-value market-price">¥{{ activityDetail.marketPrice }}</text>
          </view>
          <view class="price-item">
            <text class="price-label">会员价</text>
            <text class="price-value member-price">¥{{ activityDetail.memberPrice }}</text>
          </view>
          <view class="price-item">
            <text class="price-label">非会员价</text>
            <text class="price-value non-member-price">¥{{ activityDetail.nonMemberPrice }}</text>
          </view>
        </view>
      </view>

      <!-- 名额状态 -->
      <view class="slots-card">
        <view class="slots-header">
          <text class="slots-title">名额状态</text>
          <text class="slots-count">剩余{{ activityDetail.remainingSlots }}/{{ activityDetail.totalSlots }}</text>
        </view>

        <view class="progress-section">
          <view class="progress-label">
            <text>报名进度</text>
            <text class="progress-percent">{{ activityDetail.registrationProgress }}%</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: `${activityDetail.registrationProgress}%` }" />
          </view>
        </view>
      </view>

      <!-- 活动描述 -->
      <view class="description-card">
        <view class="description-title">
          活动介绍
        </view>
        <text class="description-text">{{ activityDetail.description }}</text>
      </view>
    </view>

    <!-- 底部报名按钮 -->
    <view class="bottom-action">
      <button
        class="register-btn" :class="getRegisterButtonClass()"
        :disabled="activityDetail.remainingSlots <= 0 || isRegistered" @click="handleRegister"
      >
        {{ getRegisterButtonText() }}
      </button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.activity-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 180rpx;
}

// 自定义导航栏
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, rgba(255, 179, 71, 0.9) 0%, rgba(255, 107, 107, 0.9) 100%);
  backdrop-filter: blur(10rpx);
  height: 200rpx;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 88rpx 32rpx 20rpx;
}

.navbar-left {
  width: 55rpx;
  height: 55rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  position: absolute;
  left: 0;
}

.navbar-title {
  width: 100%;
  display: flex;
  justify-content: center;
  font-size: 30rpx;
  font-weight: bold;
  color: #fff;
  position: relative;
  margin-top: 20rpx;
}

// 图片轮播区域
.image-section {
  position: relative;
  margin-top: 128rpx;
}

.activity-swiper {
  height: 500rpx;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.image-indicator {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

// 内容区域
.content-section {
  padding: 20rpx 32rpx;
}

// 信息卡片通用样式
.info-card,
.price-card,
.slots-card,
.description-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

// 活动基本信息
.activity-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.activity-tags {
  display: flex;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.tag {
  background: linear-gradient(135deg, #ffb347 0%, #ff6b6b 100%);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-text {
  margin-left: 12rpx;
  font-size: 28rpx;
  color: #666;
}

// 价格信息
.price-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.price-content {
  display: flex;
  justify-content: space-between;
}

.price-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.price-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.price-value {
  font-size: 32rpx;
  font-weight: bold;

  &.market-price {
    color: #999;
    text-decoration: line-through;
  }

  &.member-price {
    color: #ff6b6b;
  }

  &.non-member-price {
    color: #666;
  }
}

// 名额状态
.slots-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.slots-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.slots-count {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: bold;
}

.progress-section {
  margin-top: 20rpx;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 26rpx;
  color: #666;
}

.progress-percent {
  color: #ff6b6b;
  font-weight: bold;
}

.progress-bar {
  height: 12rpx;
  background: #e0e0e0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #ffb347);
  transition: width 0.3s ease;
}

// 活动描述
.description-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

// 底部操作区域
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  padding: 20rpx 32rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #eee;
}

.register-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;

  &.register-btn-active {
    background: linear-gradient(135deg, #ffb347 0%, #ff6b6b 100%);
    color: #fff;
    box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.4);
  }

  &.register-btn-disabled {
    background: #e0e0e0;
    color: #999;
  }
}
</style>
