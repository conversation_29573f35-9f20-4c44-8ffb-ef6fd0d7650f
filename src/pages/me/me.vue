<route lang="jsonc" type="page">
  {
    "style": {
      "navigationStyle": "custom",
      "navigationBarTitleText": "我的"
    }
  }
  </route>

<script lang="ts" setup>
import { computed, reactive, ref, watch } from 'vue'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

const storeUserInfo = computed(() => userStore.userInfo)

const userInfo = reactive({
  avatar: storeUserInfo.value.avatarUrl || '/static/images/default-avatar.png',
  nickname: storeUserInfo.value.nickName || '微信用户',
  idNumber: '微信身份认证',
})

const userAvatar = ref(userInfo.avatar)

// 监听store中用户信息的变化，同步更新本地数据
watch(
  () => storeUserInfo.value,
  (newUserInfo) => {
    userInfo.avatar = newUserInfo.avatarUrl || '/static/images/default-avatar.png'
    userInfo.nickname = newUserInfo.nickName || '微信用户'
    userAvatar.value = userInfo.avatar
  },
  { deep: true, immediate: true },
)

const myActivities = ref([
  { id: 1, image: '/static/images/default-avatar.png', title: '三点班组', time: '今天 14:30', location: '北京市朝阳区', status: '进行中', actionType: '导航' },
  { id: 2, image: '/static/images/avatar.jpg', title: 'Tab Boror', time: '昨天 16:00', location: '上海市浦东新区', status: '已完成', actionType: '发布评价' },
  { id: 3, image: '/static/images/default-avatar.png', title: '远程三不许话', time: '明天 10:00', location: '线上活动', status: '待进行', actionType: null },
  { id: 4, image: '/static/images/avatar.jpg', title: '设计分享会', time: '上周五 19:00', location: '深圳市南山区', status: '已取消', actionType: null },
])

const settingsList = ref([
  { title: '意见反馈', showBadge: false, badgeCount: 0 },
  { title: '隐私政策', showBadge: false, badgeCount: 0 },
  { title: '退出登录', showBadge: false, badgeCount: 0 },
])

function handleUserCardClick() {
  uni.navigateTo({
    url: `/pages/profile/profile?avatar=${encodeURIComponent(userAvatar.value)}&nickname=${encodeURIComponent(userInfo.nickname)}`,
    events: {
      updateUserInfo: (data: any) => {
        // 不需要手动更新本地数据，因为watch会自动同步store的变化
        console.log('用户信息已更新:', data)
      },
    },
  })
}

function handleAvatarClick() {
  handleUserCardClick()
}

function handleNicknameClick() {
  handleUserCardClick()
}

function handleSettingClick(item: any) {
  if (item.title === '意见反馈')
    uni.navigateTo({ url: '/pages/feedback/feedback' })
  else if (item.title === '隐私政策')
    uni.navigateTo({ url: '/pages/privacy/privacy' })
  else if (item.title === '退出登录')
    handleLogout()
}

function handleActivityClick(activity: any) {
  uni.showToast({
    title: `查看活动: ${activity.title}`,
    icon: 'none',
  })
}

function getStatusClass(status: string) {
  switch (status) {
    case '进行中':
      return 'bg-green-100 text-green-600 before:content-[""] before:w-3 before:h-3 before:rounded-full before:bg-green-600 before:mr-2'
    case '已完成':
      return 'bg-green-100 text-green-600 before:content-[""] before:w-3 before:h-3 before:rounded-full before:bg-green-600 before:mr-2'
    case '待进行':
      return 'bg-yellow-100 text-yellow-600 before:content-[""] before:w-3 before:h-3 before:rounded-full before:bg-yellow-600 before:mr-2'
    case '已取消':
      return 'bg-red-100 text-red-600 before:content-[""] before:w-3 before:h-3 before:rounded-full before:bg-red-600 before:mr-2'
    default:
      return ''
  }
}

function getActionClass(status: string) {
  switch (status) {
    case '进行中':
      return 'bg-blue-50 text-blue-600 border-blue-600'
    case '已完成':
      return 'bg-green-50 text-green-600 border-green-600'
    default:
      return ''
  }
}

function handleLogout() {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({ title: '已退出登录', icon: 'success' })
        setTimeout(() => {
          uni.reLaunch({ url: '/pages/login/login' })
        }, 1500)
      }
    },
  })
}

// 移除原有的头像选择逻辑，现在通过profile页面处理
</script>

<template>
  <view class="min-h-screen" style="background: linear-gradient(to bottom, #ffffff, #f7f8fa 300rpx);">
    <view class="relative overflow-hidden" style="background: linear-gradient(135deg, #ffb347 0%, #ff6b6b 100%); padding: 100rpx 40rpx 140rpx; border-radius: 0 0 80rpx 80rpx;">
      <view class="absolute z-1 rounded-full" style="content: ''; width: 300rpx; height: 300rpx; top: -100rpx; right: -100rpx; background-color: rgba(255, 255, 255, 0.1);" />
      <view class="absolute z-1 rounded-full" style="content: ''; width: 150rpx; height: 150rpx; bottom: 20rpx; left: -50rpx; background-color: rgba(255, 255, 255, 0.1);" />
      <view style="height: 88rpx;" />
    </view>
    <view style="padding: 0 30rpx;">
      <view class="relative z-10 flex items-center justify-between border border-white/50" style="margin-top: -100rpx; padding: 40rpx; border-radius: 32rpx; background-color: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20rpx); box-shadow: 0 16rpx 40rpx rgba(100, 100, 100, 0.12);">
        <view class="flex items-center" style="gap: 24rpx;">
          <view @click="handleAvatarClick">
            <image class="border-4 border-white rounded-full" style="width: 110rpx; height: 110rpx;" :src="userAvatar" mode="aspectFill" />
          </view>
          <view @click="handleNicknameClick">
            <view class="text-gray-800 font-semibold" style="font-size: 36rpx; margin-bottom: 8rpx;">
              {{ userInfo.nickname }}
            </view>
            <view class="text-gray-600" style="font-size: 24rpx;">
              {{ userInfo.idNumber }}
            </view>
          </view>
        </view>
        <view class="text-white font-medium" style="padding: 18rpx 36rpx; background: linear-gradient(135deg, #ffb347 0%, #ff6b6b 100%); border-radius: 30rpx; font-size: 26rpx; box-shadow: 0 8rpx 16rpx rgba(255, 125, 106, 0.35);">
          <text>联动者</text>
        </view>
      </view>

      <view style="margin-top: 50rpx; padding-bottom: 40rpx;">
        <view class="flex items-center justify-between" style="margin-bottom: 30rpx;">
          <view class="text-gray-800 font-semibold" style="font-size: 38rpx;">
            我参加的活动
          </view>
        </view>

        <view class="overflow-hidden" style="background: rgba(255, 255, 255, 0.9); border-radius: 24rpx; backdrop-filter: blur(10rpx);">
          <view
            v-for="activity in myActivities.slice(0, 3)"
            :key="activity.id"
            class="flex items-start border-b border-gray-100 last:border-b-0"
            style="padding: 32rpx;"
            @click="handleActivityClick(activity)"
          >
            <view class="flex-shrink-0 overflow-hidden" style="width: 120rpx; height: 120rpx; border-radius: 16rpx; margin-right: 24rpx;">
              <image :src="activity.image" mode="aspectFill" class="h-full w-full" />
            </view>

            <view class="flex flex-1 flex-col justify-between" style="height: 120rpx;">
              <view>
                <view class="text-gray-800 font-semibold leading-tight" style="font-size: 32rpx; margin-bottom: 12rpx;">
                  {{ activity.title }}
                </view>
                <view class="flex items-center" style="gap: 16rpx; margin-bottom: 16rpx;">
                  <text class="text-gray-500" style="font-size: 24rpx;">{{ activity.time }}</text>
                  <text class="text-gray-500" style="font-size: 24rpx;">{{ activity.location }}</text>
                </view>
              </view>

              <view class="flex items-center" style="gap: 16rpx;">
                <view class="flex items-center rounded-full font-medium" style="padding: 6rpx 16rpx; font-size: 22rpx;" :class="getStatusClass(activity.status)">
                  {{ activity.status }}
                </view>
                <view v-if="activity.actionType" class="border rounded-full font-medium" style="padding: 6rpx 16rpx; font-size: 22rpx;" :class="getActionClass(activity.status)">
                  {{ activity.actionType }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view style="padding-bottom: 40rpx;">
        <view class="overflow-hidden" style="background: rgba(255, 255, 255, 0.9); border-radius: 24rpx; backdrop-filter: blur(10rpx);">
          <view
            v-for="(item, index) in settingsList"
            :key="index"
            class="flex items-center justify-between border-b border-gray-100 transition-colors duration-200 last:border-b-0 active:bg-black/5"
            style="padding: 20rpx 32rpx;"
            @click="handleSettingClick(item)"
          >
            <view class="flex flex-1 items-center">
              <view class="flex-1">
                <view class="text-gray-800" style="font-size: 26rpx; margin-bottom: 4rpx;">
                  {{ item.title }}
                </view>
              </view>
            </view>
            <view>
              <uni-icons type="right" size="16" color="#C7C7CC" />
            </view>
          </view>
        </view>
      </view>

      <view style="height: 60rpx;" />
    </view>
  </view>
</template>

  <style scoped>
</style>
