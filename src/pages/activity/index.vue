<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "活动"
  }
}
</route>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'

// 日历数据
const calendarDays = ref<any[]>([])

// 我参加的活动数据
const myActivities = ref([
  {
    id: 1,
    image: '/static/images/activity1.jpg',
    title: '健康生活分享会',
    time: '2024-01-15 14:00',
    location: '社区活动中心',
    status: 'ongoing', // upcoming, ongoing, completed, cancelled
    progress: 75,
  },
  {
    id: 2,
    image: '/static/images/activity2.jpg',
    title: '瑜伽体验课',
    time: '2024-01-18 10:00',
    location: '瑜伽馆',
    status: 'upcoming',
    progress: 0,
  },
  {
    id: 3,
    image: '/static/images/activity3.jpg',
    title: '营养膳食讲座',
    time: '2024-01-12 16:00',
    location: '健康中心',
    status: 'completed',
    progress: 100,
  },
])

// 初始化日历
function initCalendar() {
  const today = new Date()
  const days = []
  const weekNames = ['日', '一', '二', '三', '四', '五', '六']

  // 生成最近10天的日期
  for (let i = 0; i < 10; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)

    const day = {
      date: date.getDate(),
      week: weekNames[date.getDay()],
      fullDate: date.toISOString().split('T')[0],
      isToday: i === 0,
      hasActivity: Math.random() > 0.6, // 随机生成活动标记
    }

    days.push(day)
  }

  calendarDays.value = days
}

// 选择日期
function selectDate(day: any) {
  console.log('选择日期:', day.fullDate)
  // 这里可以添加筛选活动的逻辑
}

// 获取状态样式类
function getStatusClass(status: string) {
  const statusMap: Record<string, string> = {
    upcoming: 'status-upcoming',
    ongoing: 'status-ongoing',
    completed: 'status-completed',
    cancelled: 'status-cancelled',
  }
  return statusMap[status] || ''
}

// 获取状态文本
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    upcoming: '即将开始',
    ongoing: '进行中',
    completed: '已完成',
    cancelled: '已取消',
  }
  return statusMap[status] || '未知'
}

// 跳转到活动详情
function goToActivityDetail(activity: any) {
  uni.navigateTo({
    url: `/pages/activityDetail/index`,
  })
}

// 探索活动
function exploreActivities() {
  uni.switchTab({
    url: '/pages/index/index',
  })
}

onMounted(() => {
  initCalendar()
})
</script>

<template>
  <view class="activity-page">
    <!-- 活动日历 -->
    <view class="calendar-section">
      <view class="section-title">
        活动日历
      </view>
      <scroll-view scroll-x class="calendar-scroll">
        <view class="calendar-container">
          <view
            v-for="(day, index) in calendarDays" :key="index" class="calendar-day"
            :class="{ 'has-activity': day.hasActivity, 'today': day.isToday }" @click="selectDate(day)"
          >
            <view class="day-week">
              {{ day.week }}
            </view>
            <view class="day-date">
              {{ day.date }}
            </view>
            <view v-if="day.hasActivity" class="activity-dot" />
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 我参加的活动 -->
    <view class="my-activities-section">
      <view class="section-title">
        我参加的活动
      </view>
      <view class="activity-list">
        <view
          v-for="(activity, index) in myActivities" :key="index" class="activity-item"
          @click="goToActivityDetail(activity)"
        >
          <image :src="activity.image" class="activity-image" mode="aspectFill" />
          <view class="activity-info">
            <view class="activity-title">
              {{ activity.title }}
            </view>
            <view class="activity-time">
              <uni-icons type="calendar" size="14" color="#666" />
              <text>{{ activity.time }}</text>
            </view>
            <view class="activity-location">
              <uni-icons type="location" size="14" color="#666" />
              <text>{{ activity.location }}</text>
            </view>
            <view class="activity-bottom">
              <view class="activity-status" :class="getStatusClass(activity.status)">
                {{ getStatusText(activity.status) }}
              </view>
              <view class="activity-progress">
                <text>完成度: {{ activity.progress }}%</text>
                <view class="progress-bar">
                  <view class="progress-fill" :style="{ width: `${activity.progress}%` }" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="myActivities.length === 0" class="empty-state">
      <uni-icons type="calendar" size="60" color="#ccc" />
      <text class="empty-text">暂无参加的活动</text>
      <button class="explore-btn" @click="exploreActivities">
        去探索活动
      </button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.activity-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding: 0 16px;
}

// 日历样式
.calendar-section {
  background: white;
  padding: 20px 0;
  margin-bottom: 12px;
}

.calendar-scroll {
  white-space: nowrap;
}

.calendar-container {
  display: flex;
  padding: 0 16px;
  gap: 12px;
}

.calendar-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
  padding: 12px 8px;
  border-radius: 12px;
  background: #f8f8f8;
  position: relative;
  transition: all 0.3s ease;

  &.today {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  &.has-activity {
    border: 2px solid #ff6b6b;
  }

  .day-week {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
  }

  .day-date {
    font-size: 16px;
    font-weight: bold;
  }

  .activity-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 6px;
    height: 6px;
    background: #ff6b6b;
    border-radius: 50%;
  }

  &.today .day-week,
  &.today .day-date {
    color: white;
  }

  &.today .activity-dot {
    background: #fff;
  }
}

// 活动列表样式
.my-activities-section {
  background: white;
  padding: 20px 0;
}

.activity-list {
  padding: 0 16px;
}

.activity-item {
  display: flex;
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.activity-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 16px;
  flex-shrink: 0;
}

.activity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.activity-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
}

.activity-time,
.activity-location {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
  margin-bottom: 6px;

  text {
    margin-left: 6px;
  }
}

.activity-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.activity-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;

  &.status-upcoming {
    background: #e3f2fd;
    color: #1976d2;
  }

  &.status-ongoing {
    background: #fff3e0;
    color: #f57c00;
  }

  &.status-completed {
    background: #e8f5e8;
    color: #388e3c;
  }

  &.status-cancelled {
    background: #ffebee;
    color: #d32f2f;
  }
}

.activity-progress {
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  text {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
  }
}

.progress-bar {
  width: 60px;
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #8bc34a);
  transition: width 0.3s ease;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  margin: 20px 16px;
  border-radius: 12px;

  .empty-text {
    font-size: 16px;
    color: #999;
    margin: 20px 0;
  }

  .explore-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-size: 14px;
  }
}
</style>
