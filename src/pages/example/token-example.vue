<script setup lang="ts">
import { computed, ref } from 'vue'
import { httpGet } from '@/http/http'
import { useTokenStore } from '@/store/token'
import { useUserStore } from '@/store/user'
import { getUserWxToken } from '@/utils/auth'

const tokenStore = useTokenStore()
const userStore = useUserStore()
const apiResult = ref('')

// 显示token的前20个字符
const displayToken = computed(() => {
  const token = getUserWxToken()
  return token ? `${token.substring(0, 20)}...` : ''
})

// 去登录页面
function goToLogin() {
  uni.navigateTo({
    url: '/pages/login/login',
  })
}

// 退出登录
async function handleLogout() {
  try {
    await tokenStore.logout()
    uni.showToast({
      title: '退出成功',
      icon: 'success',
    })
    apiResult.value = ''
  }
  catch (error) {
    console.error('退出失败:', error)
    uni.showToast({
      title: '退出失败',
      icon: 'none',
    })
  }
}

// 测试API调用（需要token）
async function testApiCall() {
  try {
    uni.showLoading({ title: '请求中...' })

    // 这里调用一个需要token的API接口
    // 例如获取用户信息或其他需要认证的接口
    const response = await httpGet('/user/profile')

    apiResult.value = JSON.stringify(response, null, 2)
    uni.hideLoading()
    uni.showToast({
      title: 'API调用成功',
      icon: 'success',
    })
  }
  catch (error) {
    uni.hideLoading()
    console.error('API调用失败:', error)
    apiResult.value = `API调用失败: ${error.message || error}`
    uni.showToast({
      title: 'API调用失败',
      icon: 'none',
    })
  }
}
</script>

<template>
  <view class="container">
    <view class="section">
      <text class="title">Token 状态</text>
      <view class="info-item">
        <text class="label">登录状态:</text>
        <text class="value" :class="tokenStore.hasLogin ? 'success' : 'error'">
          {{ tokenStore.hasLogin ? '已登录' : '未登录' }}
        </text>
      </view>
      <view v-if="tokenStore.hasLogin" class="info-item">
        <text class="label">Token:</text>
        <text class="value token">{{ displayToken }}</text>
      </view>
    </view>

    <view class="section">
      <text class="title">用户信息</text>
      <view class="info-item">
        <text class="label">昵称:</text>
        <text class="value">{{ userStore.userInfo.nickname || '未设置' }}</text>
      </view>
      <view class="info-item">
        <text class="label">用户ID:</text>
        <text class="value">{{ userStore.userInfo.userId || '未设置' }}</text>
      </view>
    </view>

    <view class="actions">
      <button
        v-if="!tokenStore.hasLogin"
        class="btn primary"
        @click="goToLogin"
      >
        去登录
      </button>

      <template v-else>
        <button class="btn secondary" @click="testApiCall">
          测试API调用
        </button>
        <button class="btn danger" @click="handleLogout">
          退出登录
        </button>
      </template>
    </view>

    <view v-if="apiResult" class="section">
      <text class="title">API调用结果</text>
      <text class="api-result">{{ apiResult }}</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
}

.section {
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
  align-items: flex-start;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;

  &.success {
    color: #4ade80;
  }

  &.error {
    color: #f87171;
  }

  &.token {
    font-family: monospace;
    font-size: 24rpx;
    background: #f5f5f5;
    padding: 8rpx;
    border-radius: 8rpx;
  }
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.btn {
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;

  &.primary {
    background: linear-gradient(135deg, #4ade80 0%, #06b6d4 100%);
    color: white;
  }

  &.secondary {
    background: #f3f4f6;
    color: #374151;
  }

  &.danger {
    background: #fef2f2;
    color: #dc2626;
    border: 2rpx solid #fecaca;
  }
}

.api-result {
  font-family: monospace;
  font-size: 24rpx;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
  white-space: pre-wrap;
  word-break: break-all;
  display: block;
}
</style>
