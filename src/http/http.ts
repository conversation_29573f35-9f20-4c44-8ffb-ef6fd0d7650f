import type { CustomRequestOptions } from '@/http/types'
import { LOGIN_PAGE } from '@/router/config'
import { useTokenStore } from '@/store/token'
import { useUserStore } from '../store/user'

export function http<T>(options: CustomRequestOptions) {
  // 1. 返回 Promise 对象
  return new Promise<IResData<T>>((resolve, reject) => {
    uni.request({
      ...options,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success: async (res) => {
        console.log('HTTP Response:', res)
        if (typeof res.data === 'object' && res.data !== null && (res.data as IResData<T>).code >= 200 && (res.data as IResData<T>).code < 300) {
          return resolve(res.data as IResData<T>)
        }
        if (typeof res.data === 'object' && res.data !== null) {
          const resData: IResData<T> = res.data as IResData<T>
          if ((resData.code === 401)) {
            const userStore = useUserStore()
            userStore.clearUserInfo()
            uni.showToast({
              title: '登录已过期，请重新登录',
              icon: 'none',
            })
            setTimeout(() => {
              uni.navigateTo({ url: LOGIN_PAGE })
            }, 1500)
            return reject(res)
          }
          else {
            // 其他错误 -> 根据后端错误信息轻提示
            !options.hideErrorToast
            && uni.showToast({
              icon: 'none',
              title: resData.msg || '请求错误',
            })
            return reject(res)
          }
        }
        else {
          // 处理 res.data 不是对象的情况
          uni.showToast({
            icon: 'none',
            title: '数据格式错误',
          })
          return reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @param header 请求头，默认为json格式
 * @returns
 */
export function httpGet<T>(url: string, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    query,
    method: 'GET',
    header,
    ...options,
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @param header 请求头，默认为json格式
 * @returns
 */
export function httpPost<T>(url: string, data?: Record<string, any>, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
    header,
    ...options,
  })
}
/**
 * PUT 请求
 */
export function httpPut<T>(url: string, data?: Record<string, any>, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    data,
    query,
    method: 'PUT',
    header,
    ...options,
  })
}

/**
 * DELETE 请求（无请求体，仅 query）
 */
export function httpDelete<T>(url: string, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    query,
    method: 'DELETE',
    header,
    ...options,
  })
}

http.get = httpGet
http.post = httpPost
http.put = httpPut
http.delete = httpDelete

// 支持与 alovaJS 类似的API调用
http.Get = httpGet
http.Post = httpPost
http.Put = httpPut
http.Delete = httpDelete
