import type { ILoginForm } from '../api/login'
import type { IAuthLoginRes } from '../api/types/login'
import { defineStore } from 'pinia'
import { computed } from 'vue'
import { logout as _logout } from '../api/login'
import { useUserStore } from './user'

export const useTokenStore = defineStore(
  'token',
  () => {
    /**
     * 处理登录API响应
     * @param loginResponse 登录API的完整响应
     * @param loginData 登录时的用户信息
     */
    const handleLoginResponse = async (
      loginResponse: IResData<IAuthLoginRes>,
      loginData: ILoginForm,
    ) => {
      if (loginResponse.code === 200) {
        console.log('loginResponse🚀🚀', loginResponse)
        const token = loginResponse.data // 修复：从data.data获取token字符串
        const userStore = useUserStore()

        // 同时存储token和用户信息到pinia和storage
        userStore.setUserInfo({
          nickName: loginData.nickName,
          avatarUrl: loginData.avatarUrl,
          gender: loginData.gender,
          userWxToken: token,
        })

        return { success: true, token }
      }
      else {
        throw new Error(loginResponse.msg || '登录失败')
      }
    }

    /**
     * 退出登录并清空用户信息
     */
    const logout = async () => {
      try {
        await _logout()
      }
      catch (error) {
        console.error('退出登录失败:', error)
      }
      finally {
        // 清空用户信息（包括token）
        const userStore = useUserStore()
        userStore.clearUserInfo()
      }
    }

    /**
     * 获取token（从用户store中获取）
     */
    const getToken = computed(() => {
      const userStore = useUserStore()
      return userStore.getUserWxToken
    })

    /**
     * 检查是否已登录
     */
    const hasLogin = computed(() => {
      const userStore = useUserStore()
      return userStore.isLoggedIn
    })

    return {
      // 核心API方法
      logout,
      handleLoginResponse,

      // 认证状态判断
      hasLogin,

      // token获取
      getToken,
    }
  },
)
