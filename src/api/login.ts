import type { IAuthLoginRes } from './types/login'
import { http } from '@/http/http'

/**
 * 登录表单
 */
export interface ILoginForm {
  avatarUrl: string
  code: string
  encryptedData: string
  gender: string
  iv: string
  nickName: string
}

/**
 * 用户登录
 * @param  loginForm 登录表单数据
 */
export function userWxLogin(loginForm: ILoginForm) {
  return http.post<IAuthLoginRes>('/api/wx/login', loginForm)
}

/**
 * 退出登录
 */
export function logout() {
  return http.get<void>('/auth/logout')
}

/**
 * 微信登录
 * @param params 微信登录参数，包含code
 * @returns Promise 包含登录结果
 */
export function wxLogin(data: { code: string }) {
  return http.post<IAuthLoginRes>('/auth/wxLogin', data)
}

/**
 * 获取微信登录凭证
 * @returns Promise 包含微信登录凭证(code)
 */
export function getLoginCode() {
  return new Promise<UniApp.LoginRes>((resolve, reject) => {
    uni.login({
      success: (res) => {
        console.log('获取登录凭证成功:', res)
        resolve(res)
      },
      fail: (err) => {
        console.error('获取登录凭证失败:', err)
        const errorMessage = typeof err === 'string'
          ? err
          : err?.errMsg || err?.message || JSON.stringify(err) || '获取登录凭证失败'
        reject(new Error(errorMessage))
      },
    })
  })
}

// 用户信息接口
export interface IUserInfo {
  nickName: string
  avatarUrl: string
  gender: number
}

// 获取用户信息
export function getUserInfo() {
  return new Promise<IUserInfo>((resolve, reject) => {
    uni.getUserInfo({
      success: (res) => {
        const userInfo: IUserInfo = {
          nickName: res.userInfo.nickName,
          avatarUrl: res.userInfo.avatarUrl,
          gender: (res.userInfo as any).gender ?? 0,
        }
        resolve(userInfo)
      },
      fail: (err) => {
        console.error('获取用户信息失败:', err)
        const errorMessage = typeof err === 'string'
          ? err
          : err?.errMsg || err?.message || JSON.stringify(err) || '获取用户信息失败'
        reject(new Error(errorMessage))
      },
    })
  })
}
