// 登录API响应结构
export interface IAuthLoginRes {
  code: number
  msg: string
  data: string // token字符串
}

// Token响应类型（用于内部存储）
export interface ITokenRes {
  Token: string
}

/**
 * 用户信息（保留原有接口，用于兼容）
 */
export interface IUserInfoRes {
  userId: number
  username: string
  nickname: string
  avatar?: string
  [key: string]: any // 允许其他扩展字段
}

// 认证存储数据结构
export interface AuthStorage {
  tokens: ITokenRes
  userInfo?: IUserInfoRes
  loginTime: number // 登录时间戳
}

/**
 * 上传成功的信息
 */
export interface IUploadSuccessInfo {
  fileId: number
  originalName: string
  fileName: string
  storagePath: string
  fileHash: string
  fileType: string
  fileBusinessType: string
  fileSize: number
}
