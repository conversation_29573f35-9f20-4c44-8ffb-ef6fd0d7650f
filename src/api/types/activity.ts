export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

export interface ActivityResult {
  id: number
  activityNo: string
  title: string
  activityImage: string
  startTime: string
  endTime: string
  location: string
  activityQuota: number
  attendanceCount: number | null
  activityDetails: string
  status: number
  marketPrice: number
  memberPrice: number
  nonMemberPrice: number
}

export interface ActivityListResponse {
  total: number
  rows: ActivityResult[]
  code: number
  msg: string
}

export interface activity {
  pageNum: number
  pageSize: number
}
