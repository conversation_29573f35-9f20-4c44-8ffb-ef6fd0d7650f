# Token 存储测试指南

## 验证Token存储功能

### 1. 测试步骤

1. **登录测试**
   - 在登录页面完成微信登录
   - 检查控制台输出，确认token已获取
   - 验证登录状态

2. **存储验证**
   - 使用开发者工具查看本地存储
   - 检查是否存在key为'token'的数据
   - 验证数据格式是否正确

3. **持久化测试**
   - 关闭并重新打开小程序
   - 检查登录状态是否保持
   - 验证token是否自动恢复

### 2. 调试方法

```javascript
// 在控制台中执行以下代码来检查token存储

// 1. 检查pinia store中的token
const tokenStore = useTokenStore()
console.log('Store中的token:', tokenStore.getToken)
console.log('登录状态:', tokenStore.hasLogin)

// 2. 检查本地存储
const tokenData = uni.getStorageSync('token')
console.log('本地存储的token数据:', tokenData)

// 3. 检查所有存储的keys
const storageInfo = uni.getStorageInfoSync()
console.log('所有存储的keys:', storageInfo.keys)
```

### 3. 预期结果

**登录成功后：**
- `tokenStore.getToken` 应该返回JWT token字符串
- `tokenStore.hasLogin` 应该返回 `true`
- `uni.getStorageSync('token')` 应该返回包含tokenInfo的对象

**重启应用后：**
- token状态应该自动恢复
- 不需要重新登录
- API请求应该自动携带token

### 4. 存储数据结构

```json
{
  "tokenInfo": {
    "Token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxODIwMjY3MTg2MSIsImxvZ2luX3VzZXJfa2V5IjoiODRiNzY0YTgtZmM1Ni00NzdjLWE3MDAtYTU1NmFlZDViNTI4In0.xrFr4fCuwqXqv4yhzQ_JPbeIMFJIEqj-Zba_egS5cTYISeH44XGTDZbUeSVHUJn6B6sn5Rv0xhk0NUtPs2a1Sw"
  }
}
```

### 5. 常见问题排查

**问题1：Token没有存储**
- 检查pinia持久化插件是否正确配置
- 确认store的persist配置是否正确
- 验证uni.setStorageSync是否正常工作

**问题2：重启后token丢失**
- 检查persist配置中的paths是否包含tokenInfo
- 确认storage配置是否使用了正确的uni-app API
- 验证key是否唯一且正确

**问题3：Token格式错误**
- 检查API响应格式是否匹配
- 确认handleLoginResponse方法是否正确解析
- 验证setTokenInfo方法是否正确设置

### 6. 测试页面

可以访问 `/pages/test-token/test-token` 页面进行完整的token存储测试。

该页面提供了以下功能：
- 模拟设置token
- 检查token状态
- 验证本地存储
- 清除token测试